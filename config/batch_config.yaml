# 文件目录相关配置
output_dir: "data/output"
result_csv: "flow_speed_results.csv"
pixel_to_meter: 0.0084 # 全局像素与米转换系数，五乡是0.09，阿育王是0.0084，西屋是0.0065

batch_settings:
  input_videos:
    - 'data/video/ch01_20250414145900.mp4'
    - 'data/video/ch01_20250420085900.mp4'
    - 'data/video/ch01_20250420125900.mp4'
    - 'data/video/ch01_20250421235900.mp4'
    - 'data/video/ch01_20250422005900.mp4'
    - 'data/video/ch01_20250506075900.mp4'
    - 'data/video/ch01_20250506115900.mp4'
    - 'data/video/ch01_20250506145900.mp4'
    - 'data/video/0812_speed_720.mp4'
  time_ranges: # 这些是全局的，适用于批处理中的所有视频
  #  - ['0:10', '1:00'] # 西屋
    # - ['0:25', '0:40'] # 阿育王站旧
    # - ['1:40', '1:56'] # 阿育王站旧
    # - ['1:40', '2:20'] # 阿育王站旧
    - ['0:10', '1:00'] # 阿育王站新
    # - ['0:10', '0:40'] # 五香站
analysis_method: "piv" # 可选"piv", "otv"

roi_points: [[550,150],[1000,150],[1250,250],[1250,500],[1100,500],[900,350],[800,350]] # 阿育王站旧
# roi_points: [[800,660],[1800,660],[1800,820],[800,820]] # 五香站
# roi_points: [[500,50],[1200,50],[1200,700],[500,700]] # 西屋

# 'piv' 方法的特定参数 (后续实现将使用这些参数)
piv_params:
  # 指向 OpenPIV 分析器本身使用的更详细的JSON配置文件
  piv_config_json_path: "config/piv_config.json"

# 'otv' (OpenCV Optical Track Velocimetry) 方法的特定参数
otv_params:
  mode: "real" # 可选 "pre" 或 "real"
  input_video_path: "data/video/0812_speed_720.mp4" # 输入视频路径
  check_feature_points: false # 是否检查特征点
  direction_concentration_range: 180 # 方向集中度过滤范围（度），保留最集中方向±45度范围内的特征点
  min_feature_points_rediscover: 5 # 特征点少于此值时重新检测
  speed_adjustment_factor: 5.6 # 模型微调参数
  add_visual_elements_to_trimmed_video: false # 是否在裁剪的视频中添加可视化元素
  add_grid_to_output_video: false # 是否在OTV算法输出视频中添加网格
  feature_points_option: valid # 可选 "all", "valid"
  trajectory_static_threshold: 10.0 # 轨迹静态过滤阈值（像素），小于此值的总位移被视为静态点
  quartile_filter_min_points: [5, 10] # 分位法过滤的数据点阈值 [A, B]: A<=数据量<B时用八分位，数据量>=B时用四分位，数据量<A时不过滤
  
  # 特征检测方案配置
  feature_detection_methods: ["orb", "goodFeaturesToTrack"] # 特征检测方案列表，第一个为优先方案，第二个为备用方案
  enable_fallback: true # 是否启用备用方案，当优先方案失败时使用备用方案
  
  # 时间窗口动态过滤参数
  window_avg_time_seconds: 1.0 # 时间窗口大小（秒），用于计算特征点平均位移
  motion_decay_threshold: 0.1 # 运动衰减阈值（10%），当窗口平均位移低于第一个窗口的此百分比时视为静止
  
  # 过滤层级启用控制参数
  filter_enable_layers: [false, true, false, true, true] # 控制5个过滤层的启用状态
  # [0]: 初始特征筛选 - 过滤网格交点、文字区域、静态区域
  # [1]: 方向过滤 - 基于方向集中度过滤
  # [2]: 轨迹静态点过滤 - 基于总位移阈值过滤静态点
  # [3]: 分位法过滤 - 基于四分位法或八分位法过滤异常值
  # [4]: 时间窗口动态过滤 - 基于运动衰减过滤
  
  # 智能起始帧选择参数
  max_search_frames: 50 # 最大搜索帧数，用于寻找合适的起始帧
  frame_quality_threshold: 0.3 # 帧质量阈值，用于选择起始帧（0-1之间，越高要求越严格）
  
  # ORB备选特征检测器参数
  orb_fallback_enabled: true # 是否启用ORB作为备选特征检测器
  orb_nfeatures: 2000 # ORB检测器的最大特征点数量
  orb_search_frames: 5 # ORB检测器搜索的最大帧数
  orb_quality_threshold_offset: -0.1 # ORB质量阈值相对于主阈值的偏移量（负值表示更宽松）
  
  # 特征识别失败处理参数
  save_failed_detection_video: true # 当两种特征识别方法都失败时，是否保存无过滤的识别帧视频
  
  # 优化的特征点检测参数
  feature_params:
    maxCorners: 500 # 最大角点数，从200增加到500，或设置为0表示无限制
    qualityLevel: 0.05 # 质量水平，从0.3降低到0.01，检测更多特征点
    minDistance: 5 # 最小距离，从7降低到5，允许邻近区域更多检测
    blockSize: 7 # 块大小，新增参数，优化检测精度
  