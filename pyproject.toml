[project]
name = "flowspeed"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.10"
dependencies = [
    "awive",
    "cython>=3.1.0",
    "h5py>=3.13.0",
    "imageio>=2.37.0",
    "imread>=0.7.6",
    "jupyter>=1.1.1",
    "matplotlib>=3.10.1",
    "opencv-python>=********",
    "opencv-stubs>=0.0.12",
    "openpiv>=0.25.3",
    "pandas>=2.3.1",
    "pyfftw>=0.15.0",
    "pyyaml>=6.0.2",
    "scikit-image>=0.25.2",
    "scikit-learn>=1.6.1",
    "scipy>=1.15.3",
    "setuptools>=80.3.1",
    "typer>=0.15.2",
]

[[tool.uv.index]]
url = "https://pypi.tuna.tsinghua.edu.cn/simple"
default = true

[tool.uv.sources]
awive = { path = "lib/awive", editable = true }
