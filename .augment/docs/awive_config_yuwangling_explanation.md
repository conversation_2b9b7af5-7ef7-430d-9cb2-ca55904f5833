# AWIVE配置文件说明 - 雨汪岭视频流速估算

本文档说明了为视频 `data/video/0812_speed_720.mp4` 创建的AWIVE算法配置文件 `config/awive_config_yuwangling.json` 的参数设置和优化策略。

## 视频特征
- **目标视频**: `data/video/0812_speed_720.mp4`
- **海拔高度**: 约5米
- **预期流速范围**: 0.05-0.5 m/s（慢流速场景）
- **河道方向**: 非水平方向（从左上到右下）

## 关键配置参数说明

### 1. 地面控制点（GCP）配置
```json
"gcp": {
  "apply": true,
  "pixels": [[1260, 180], [1000, 130], [75, 80], [745, 500]],
  "distances": {
    "(0,1)": 4.6,
    "(0,2)": 15.1,
    "(0,3)": 14.8,
    "(1,2)": 10.7,
    "(1,3)": 13.5,
    "(2,3)": 12.6
  }
}
```

**设置说明**:
- **启用GCP校正** (`apply: true`): 提高测量精度
- **像素坐标**: 按右下角、右上角、左上角、左下角顺序排列
- **距离计算**: 使用Haversine公式从WGS84经纬度坐标计算得出
- **原始经纬度坐标**:
  - 右下角: [121.746625, 29.858377]
  - 右上角: [121.746658, 29.858407]
  - 左上角: [121.746700, 29.858496]
  - 左下角: [121.746569, 29.858501]

### 2. 图像预处理配置
```json
"preprocessing": {
  "pre_roi": [[70, 75], [600, 1240]],
  "roi": [[80, 100], [550, 1200]],
  "ppm": 25
}
```

**设置说明**:
- **pre_roi**: 基于稳定化区域 `[775, 600], [75, 80], [750, 70], [1240, 140]` 设定
- **roi**: 在pre_roi基础上进一步精确到主要水面区域
- **ppm**: 设置为25，适合5米海拔高度的像素密度

### 3. STIV算法优化（慢流速场景）
```json
"stiv": {
  "lines_range": [[100, 1200], [150, 1150], [200, 1100]],
  "polar_filter_width": 18
}
```

**慢流速优化**:
- **polar_filter_width**: 从默认10增加到18，提高对慢速运动的敏感度
- **lines_range**: 根据河道非水平方向调整，X范围逐渐收窄以适应河道形状

### 4. 水位检测区域
```json
"water_level": {
  "roi": [[200, 300], [400, 1000]],
  "roi2": [[150, 250], [350, 950]]
}
```

**设置说明**:
- 选择稳定的水面区域进行水位检测
- 避开岸边和可能的干扰区域

### 5. 流速计算线
```json
"lines": [250, 300, 350]
```

**设置说明**:
- 在ROI范围内设置3条水平线
- 位置选择避开边界效应，确保在稳定水流区域

## 参数优化策略

### 针对慢流速场景的优化
1. **增大polar_filter_width**: 18（默认10）提高慢速运动检测能力
2. **调整water_flow.area**: 8.0（相比参考配置的10.0略小）适应较小的有效流动区域
3. **精确的ROI设置**: 确保分析区域集中在主要水流区域

### 针对河道非水平方向的优化
1. **lines_range渐变设计**: X范围从[100,1200]到[200,1100]，适应河道走向
2. **多层次ROI**: pre_roi和roi的层次设计，确保捕获完整河道信息

## 预期效果
- 通过GCP校正提高测量精度
- 针对慢流速优化的参数设置应能有效检测0.05-0.5 m/s的流速
- 河道方向适配的lines_range设置应能更好地跟踪非水平流动

## 使用方法
```bash
python awive_calculation.py
```

配置文件将自动被算法加载并应用相应的参数优化。
