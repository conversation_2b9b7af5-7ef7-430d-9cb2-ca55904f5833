# FlowSpeed项目文档索引

## 📚 文档导航

### 🎯 主要文档

#### AWIVE流速估算
- **[AWIVE综合指南](awive_comprehensive_guide.md)** ⭐ **推荐使用**
  - 雨汪岭视频流速估算的完整解决方案
  - 包含问题分析、配置优化、使用指南
  - 合并了所有AWIVE相关的技术发现

### 📋 任务总结
- **[AWIVE任务总结](awive_yuwangling_task_summary.md)**
  - 项目完成状态和主要成果
  - 技术亮点和创新点总结

### 🔧 技术工具
- **自动视频截取**: `src/utils/video_auto_trimmer.py`
- **距离计算工具**: `calculate_distances.py`
- **优化配置文件**: `config/awive_config_yuwangling.json`

## 📁 文档状态

### ✅ 当前有效文档
| 文档名称 | 用途 | 状态 |
|---------|------|------|
| `awive_comprehensive_guide.md` | AWIVE综合指南 | ✅ 最新 |
| `awive_yuwangling_task_summary.md` | 任务总结 | ✅ 有效 |

### ⚠️ 已过时文档
以下文档已被合并到综合指南中，保留作为历史记录：

| 文档名称 | 原用途 | 状态 |
|---------|--------|------|
| `awive_config_yuwangling_explanation.md` | 配置说明 | ⚠️ 已过时 |
| `awive_yuwangling_analysis_report.md` | 分析报告 | ⚠️ 已过时 |
| `awive_visualization_analysis.md` | 可视化分析 | ⚠️ 已过时 |
| `awive_optimization_solutions.md` | 优化解决方案 | ⚠️ 已过时 |
| `awive_deep_analysis_final_report.md` | 最终报告 | ⚠️ 已过时 |

## 🚀 快速开始

### 运行AWIVE分析
```bash
# 使用优化配置运行分析
python awive_calculation.py
```

### 查看结果
```bash
# 可视化图像
ls data/output/0812_speed_720_visualization_*.png

# 处理过程图像
ls data/output/images/
```

### 测试新功能
```bash
# 测试自动视频截取
python src/utils/video_auto_trimmer.py
```

## 📖 使用建议

1. **新用户**: 直接阅读 `awive_comprehensive_guide.md`
2. **技术细节**: 参考代码注释和配置文件
3. **问题排除**: 查看综合指南中的故障排除章节
4. **历史信息**: 如需详细的开发过程，可参考已过时文档

## 🔄 文档维护

- **主文档**: `awive_comprehensive_guide.md` - 定期更新
- **过时文档**: 标记为已过时，保留作为历史记录
- **版本控制**: 通过Git跟踪所有文档变更

---

**最后更新**: 2025-09-03  
**维护者**: FlowSpeed项目团队  
**文档版本**: v1.0
